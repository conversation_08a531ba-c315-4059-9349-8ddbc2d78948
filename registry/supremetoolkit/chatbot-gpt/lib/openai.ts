import { getModuleConfig } from '@/config';
import type { 
  ChatbotMessage, 
  ChatCompletionRequest, 
  ChatCompletionResponse,
  StreamingChatResponse,
  ChatbotConfig 
} from '../types';

// ============================================================================
// OPENAI CLIENT SETUP
// ============================================================================

const config = getModuleConfig('chatbot') as ChatbotConfig;

if (!config?.openaiApiKey) {
  throw new Error('OpenAI API key is missing. Please add OPENAI_API_KEY to your config.');
}

const OPENAI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/openai/';

// ============================================================================
// OPENAI HELPERS
// ============================================================================

export const openaiConfig = {
  apiKey: config.openaiApiKey,
  model: config.model || 'gpt-3.5-turbo',
  maxTokens: config.maxTokens || 1000,
  temperature: config.temperature || 0.7,
  systemPrompt: config.systemPrompt || 'You are a helpful AI assistant.',
  enableStreaming: config.enableStreaming ?? true,
};

/**
 * Convert chatbot messages to OpenAI format
 */
export function formatMessagesForOpenAI(
  messages: ChatbotMessage[],
  systemPrompt?: string
): Array<{ role: 'user' | 'assistant' | 'system'; content: string }> {
  const formattedMessages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }> = [];

  // Add system prompt if provided
  if (systemPrompt) {
    formattedMessages.push({
      role: 'system',
      content: systemPrompt,
    });
  }

  // Add conversation messages (exclude system messages from conversation)
  messages
    .filter(msg => msg.role !== 'system')
    .forEach(msg => {
      formattedMessages.push({
        role: msg.role,
        content: msg.content,
      });
    });

  return formattedMessages;
}

/**
 * Send a chat completion request to OpenAI
 */
export async function sendChatCompletion(
  messages: ChatbotMessage[],
  options: {
    systemPrompt?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
    userId?: string;
  } = {}
): Promise<Response> {
  const formattedMessages = formatMessagesForOpenAI(messages, options.systemPrompt);

  const requestBody: ChatCompletionRequest = {
    messages: formattedMessages,
    model: options.model || openaiConfig.model,
    max_tokens: options.maxTokens || openaiConfig.maxTokens,
    temperature: options.temperature ?? openaiConfig.temperature,
    stream: options.stream ?? openaiConfig.enableStreaming,
    user: options.userId,
  };

  const response = await fetch(OPENAI_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${openaiConfig.apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error?.message || 
      `OpenAI API error: ${response.status} ${response.statusText}`
    );
  }

  return response;
}

/**
 * Process streaming response from OpenAI
 */
export async function* processStreamingResponse(
  response: Response
): AsyncGenerator<string, void, unknown> {
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('No response body');
  }

  const decoder = new TextDecoder();
  let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        
        if (trimmedLine === '') continue;
        if (trimmedLine === 'data: [DONE]') return;
        if (!trimmedLine.startsWith('data: ')) continue;

        try {
          const jsonStr = trimmedLine.slice(6); // Remove 'data: ' prefix
          const data: StreamingChatResponse = JSON.parse(jsonStr);
          
          const content = data.choices[0]?.delta?.content;
          if (content) {
            yield content;
          }
        } catch (error) {
          console.warn('Error parsing streaming response:', error);
          continue;
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * Get non-streaming response from OpenAI
 */
export async function getChatCompletion(
  messages: ChatbotMessage[],
  options: {
    systemPrompt?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    userId?: string;
  } = {}
): Promise<{
  content: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}> {
  const response = await sendChatCompletion(messages, {
    ...options,
    stream: false,
  });

  const data: ChatCompletionResponse = await response.json();
  
  return {
    content: data.choices[0]?.message?.content || '',
    usage: data.usage,
  };
}

/**
 * Estimate token count for messages (rough approximation)
 */
export function estimateTokenCount(messages: ChatbotMessage[]): number {
  const text = messages.map(msg => msg.content).join(' ');
  // Rough approximation: 1 token ≈ 4 characters
  return Math.ceil(text.length / 4);
}

/**
 * Calculate approximate cost based on token usage
 */
export function calculateCost(
  model: string,
  promptTokens: number,
  completionTokens: number
): number {
  // Pricing as of 2024 (per 1K tokens)
  const pricing: Record<string, { prompt: number; completion: number }> = {
    'gpt-3.5-turbo': { prompt: 0.0015, completion: 0.002 },
    'gpt-4': { prompt: 0.03, completion: 0.06 },
    'gpt-4-turbo': { prompt: 0.01, completion: 0.03 },
    'gpt-4o': { prompt: 0.005, completion: 0.015 },
  };

  const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
  
  const promptCost = (promptTokens / 1000) * modelPricing.prompt;
  const completionCost = (completionTokens / 1000) * modelPricing.completion;
  
  return promptCost + completionCost;
}

/**
 * Validate OpenAI API key
 */
export async function validateApiKey(apiKey: string): Promise<boolean> {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });
    
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Handle OpenAI API errors
 */
export function handleOpenAIError(error: any): string {
  if (error.message?.includes('rate limit')) {
    return 'Rate limit exceeded. Please try again later.';
  }
  
  if (error.message?.includes('insufficient_quota')) {
    return 'OpenAI API quota exceeded. Please check your billing.';
  }
  
  if (error.message?.includes('invalid_api_key')) {
    return 'Invalid OpenAI API key. Please check your configuration.';
  }
  
  if (error.message?.includes('model_not_found')) {
    return 'The requested model is not available.';
  }
  
  return error.message || 'An error occurred while processing your request.';
}
