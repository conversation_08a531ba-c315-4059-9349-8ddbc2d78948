"use client";

import { useState, useCallback, useRef } from 'react';
import { sendChatCompletion, processStreamingResponse, handleOpenAIError, openaiConfig } from '../lib/openai';
import type { ChatbotMessage, UseChatbotOptions, UseChatbotReturn } from '../types';

export function useChatbot({
  conversationId,
  userId,
  systemPrompt,
  onError,
  onMessageComplete,
}: UseChatbotOptions): UseChatbotReturn {
  const [messages, setMessages] = useState<ChatbotMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(conversationId || null);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const streamingMessageRef = useRef<string>('');

  // Generate conversation ID if not provided
  const getConversationId = useCallback(() => {
    if (!currentConversationId) {
      const newId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setCurrentConversationId(newId);
      return newId;
    }
    return currentConversationId;
  }, [currentConversationId]);

  // Send message to chatbot
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading || isStreaming) return;

    const userMessage: ChatbotMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    // Add user message to conversation
    setMessages(prev => [...prev, userMessage]);
    setError(null);
    setIsLoading(true);

    // Create assistant message placeholder
    const assistantMessageId = `msg_${Date.now() + 1}_${Math.random().toString(36).substring(2, 11)}`;
    const assistantMessage: ChatbotMessage = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true,
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // Create abort controller for this request
      abortControllerRef.current = new AbortController();
      
      const currentMessages = [...messages, userMessage];
      
      if (openaiConfig.enableStreaming) {
        setIsStreaming(true);
        streamingMessageRef.current = '';

        const stream = await sendChatCompletion(currentMessages, {
          systemPrompt: systemPrompt || openaiConfig.systemPrompt,
          stream: true,
          userId,
        });

        // Process streaming response
        if (stream && 'controller' in stream) {
          // This is a streaming response
          for await (const chunk of processStreamingResponse(stream as any)) {
            if (abortControllerRef.current?.signal.aborted) {
              break;
            }

            streamingMessageRef.current += chunk;

            // Update the assistant message with streaming content
            setMessages(prev =>
              prev.map(msg =>
                msg.id === assistantMessageId
                  ? { ...msg, content: streamingMessageRef.current }
                  : msg
              )
            );
          }
        }

        // Finalize the streaming message
        const finalMessage: ChatbotMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: streamingMessageRef.current,
          timestamp: new Date(),
          isStreaming: false,
        };

        setMessages(prev =>
          prev.map(msg =>
            msg.id === assistantMessageId ? finalMessage : msg
          )
        );

        onMessageComplete?.(finalMessage);
      } else {
        // Non-streaming response
        const { getChatCompletion } = await import('../lib/openai');
        const result = await getChatCompletion(currentMessages, {
          systemPrompt: systemPrompt || openaiConfig.systemPrompt,
          userId,
        });

        const finalMessage: ChatbotMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: result.content,
          timestamp: new Date(),
          isStreaming: false,
          metadata: {
            model: openaiConfig.model,
            tokens: result.usage.total_tokens,
          },
        };

        setMessages(prev => 
          prev.map(msg => 
            msg.id === assistantMessageId ? finalMessage : msg
          )
        );

        onMessageComplete?.(finalMessage);
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        // Request was aborted
        setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
      } else {
        const errorMessage = await handleOpenAIError(err);
        setError(errorMessage);
        onError?.(errorMessage);

        // Update assistant message with error
        setMessages(prev =>
          prev.map(msg =>
            msg.id === assistantMessageId
              ? {
                  ...msg,
                  content: 'Sorry, I encountered an error. Please try again.',
                  isStreaming: false,
                  metadata: { error: errorMessage }
                }
              : msg
          )
        );
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      abortControllerRef.current = null;
      streamingMessageRef.current = '';
    }
  }, [messages, isLoading, isStreaming, systemPrompt, userId, onError, onMessageComplete]);

  // Regenerate last response
  const regenerateLastResponse = useCallback(async () => {
    if (messages.length < 2) return;

    // Find the last user message
    const lastUserMessageIndex = messages.findLastIndex(msg => msg.role === 'user');
    if (lastUserMessageIndex === -1) return;

    const lastUserMessage = messages[lastUserMessageIndex];
    
    // Remove messages after the last user message
    const messagesUpToUser = messages.slice(0, lastUserMessageIndex + 1);
    setMessages(messagesUpToUser);

    // Resend the last user message
    await sendMessage(lastUserMessage.content);
  }, [messages, sendMessage]);

  // Stop generation
  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([]);
    setError(null);
    setCurrentConversationId(null);
    
    // Stop any ongoing generation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    messages,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    clearConversation,
    regenerateLastResponse,
    stopGeneration,
    conversationId: getConversationId(),
  };
}
